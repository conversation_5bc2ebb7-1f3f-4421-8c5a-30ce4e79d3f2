package storage

import (
	"bytes"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/google/uuid"

	"shikeyinxiang/internal/config"
)

type R2Storage struct {
	client *s3.S3
	config *config.R2Config
}

// NewR2Storage 创建R2存储客户端
func NewR2Storage(cfg *config.R2Config) (*R2Storage, error) {
	// 创建AWS会话配置
	awsConfig := &aws.Config{
		Credentials: credentials.NewStaticCredentials(
			cfg.AccessKeyID,
			cfg.SecretAccessKey,
			"", // token
		),
		Endpoint:         aws.String(cfg.Endpoint),
		Region:           aws.String(cfg.Region),
		S3ForcePathStyle: aws.Bool(true), // Cloudflare R2需要路径样式
	}

	// 创建会话
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}

	// 创建S3客户端
	client := s3.New(sess)

	return &R2Storage{
		client: client,
		config: cfg,
	}, nil
}

// UploadFile 上传文件到R2
func (r *R2Storage) UploadFile(data []byte, filename, contentType string) (string, error) {
	// 生成唯一的文件名
	ext := filepath.Ext(filename)
	uniqueFilename := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	
	// 根据文件类型生成路径
	var keyPath string
	if strings.HasPrefix(contentType, "image/") {
		keyPath = fmt.Sprintf("images/%s/%s", 
			time.Now().Format("2006/01/02"), 
			uniqueFilename)
	} else {
		keyPath = fmt.Sprintf("files/%s/%s", 
			time.Now().Format("2006/01/02"), 
			uniqueFilename)
	}

	// 上传参数
	input := &s3.PutObjectInput{
		Bucket:      aws.String(r.config.BucketName),
		Key:         aws.String(keyPath),
		Body:        bytes.NewReader(data),
		ContentType: aws.String(contentType),
		ACL:         aws.String("public-read"), // 设置为公开读取
	}

	// 执行上传
	_, err := r.client.PutObject(input)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to R2: %w", err)
	}

	// 返回文件URL
	return r.GetFileURL(keyPath), nil
}

// GetFileURL 获取文件的公开访问URL
func (r *R2Storage) GetFileURL(key string) string {
	if r.config.PublicDomain != "" {
		// 使用自定义域名
		return fmt.Sprintf("%s/%s", strings.TrimRight(r.config.PublicDomain, "/"), key)
	}
	
	// 使用默认R2域名
	return fmt.Sprintf("%s/%s/%s", 
		strings.TrimRight(r.config.Endpoint, "/"), 
		r.config.BucketName, 
		key)
}

// DeleteFile 删除文件
func (r *R2Storage) DeleteFile(key string) error {
	input := &s3.DeleteObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(key),
	}

	_, err := r.client.DeleteObject(input)
	if err != nil {
		return fmt.Errorf("failed to delete file from R2: %w", err)
	}

	return nil
}

// DownloadFile 下载文件
func (r *R2Storage) DownloadFile(key string) ([]byte, error) {
	input := &s3.GetObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(key),
	}

	result, err := r.client.GetObject(input)
	if err != nil {
		return nil, fmt.Errorf("failed to download file from R2: %w", err)
	}
	defer result.Body.Close()

	data, err := io.ReadAll(result.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read file data: %w", err)
	}

	return data, nil
}

// FileExists 检查文件是否存在
func (r *R2Storage) FileExists(key string) (bool, error) {
	input := &s3.HeadObjectInput{
		Bucket: aws.String(r.config.BucketName),
		Key:    aws.String(key),
	}

	_, err := r.client.HeadObject(input)
	if err != nil {
		// 如果是404错误，说明文件不存在
		if strings.Contains(err.Error(), "NotFound") {
			return false, nil
		}
		return false, fmt.Errorf("failed to check file existence: %w", err)
	}

	return true, nil
}
