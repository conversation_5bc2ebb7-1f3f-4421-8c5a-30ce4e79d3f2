package middleware

import (
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/common/errors"
	"shikeyinxiang/internal/common/response"
)

// ErrorHandlerMiddleware 全局错误处理中间件 - 匹配Java版本GlobalExceptionHandler
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic详情
				log.Printf("Panic recovered: %v\n%s", err, debug.Stack())
				
				// 返回500错误
				response.InternalServerError(c, "系统内部错误")
				c.Abort()
			}
		}()

		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err
			handleError(c, err)
		}
	}
}

// handleError 处理错误 - 匹配Java版本的异常处理逻辑
func handleError(c *gin.Context, err error) {
	// 如果是业务错误
	if businessErr, ok := err.(*errors.BusinessError); ok {
		httpStatus := errors.GetHTTPStatus(businessErr.GetCode())
		
		// 记录业务错误日志
		log.Printf("Business error occurred: [%d] %s", businessErr.GetCode(), businessErr.GetMessage())
		
		// 返回业务错误响应
		c.JSON(httpStatus, response.ApiResponse{
			Code:    businessErr.GetCode(),
			Message: businessErr.GetMessage(),
			Data:    nil,
		})
		return
	}

	// 其他类型的错误
	log.Printf("System error occurred: %v", err)
	
	// 返回通用错误响应
	response.InternalServerError(c, "系统内部错误")
}

// NotFoundHandler 404处理器
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		response.NotFound(c, "接口不存在")
	}
}

// MethodNotAllowedHandler 405处理器
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, response.ApiResponse{
			Code:    405,
			Message: "方法不允许",
			Data:    nil,
		})
	}
}

// ValidationErrorHandler 参数验证错误处理
func ValidationErrorHandler(c *gin.Context, err error) {
	log.Printf("Validation error: %v", err)
	response.BadRequest(c, "参数验证失败: "+err.Error())
}

// AbortWithBusinessError 中断请求并返回业务错误
func AbortWithBusinessError(c *gin.Context, businessErr *errors.BusinessError) {
	httpStatus := errors.GetHTTPStatus(businessErr.GetCode())
	
	c.JSON(httpStatus, response.ApiResponse{
		Code:    businessErr.GetCode(),
		Message: businessErr.GetMessage(),
		Data:    nil,
	})
	c.Abort()
}

// AbortWithError 中断请求并返回错误
func AbortWithError(c *gin.Context, code int, message string) {
	businessErr := errors.NewBusinessError(code, message)
	AbortWithBusinessError(c, businessErr)
}
