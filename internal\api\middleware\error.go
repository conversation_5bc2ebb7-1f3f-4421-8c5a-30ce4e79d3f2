package middleware

import (
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/common/response"
)

// ErrorHandlerMiddleware 全局错误处理中间件 - 匹配Java版本GlobalExceptionHandler
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic详情
				log.Printf("Panic recovered: %v\n%s", err, debug.Stack())
				
				// 返回500错误
				response.InternalServerError(c, "系统内部错误")
				c.Abort()
			}
		}()

		c.Next()

		// 检查是否有错误
		if len(c.<PERSON>rro<PERSON>) > 0 {
			err := c.Errors.Last().Err
			handleError(c, err)
		}
	}
}

// handleError 处理错误 - 简化版本
func handleError(c *gin.Context, err error) {
	// 记录错误日志
	log.Printf("Error occurred: %v", err)

	// 返回通用错误响应
	response.InternalServerError(c, "系统内部错误")
}

// NotFoundHandler 404处理器
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		response.NotFound(c, "接口不存在")
	}
}

// MethodNotAllowedHandler 405处理器
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, response.ApiResponse{
			Code:    405,
			Message: "方法不允许",
			Data:    nil,
		})
	}
}

// ValidationErrorHandler 参数验证错误处理
func ValidationErrorHandler(c *gin.Context, err error) {
	log.Printf("Validation error: %v", err)
	response.BadRequest(c, "参数验证失败: "+err.Error())
}

// AbortWithError 中断请求并返回错误
func AbortWithError(c *gin.Context, code int, message string) {
	response.ErrorWithMessage(c, code, message)
	c.Abort()
}
