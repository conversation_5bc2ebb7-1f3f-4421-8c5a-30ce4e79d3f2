package errors

import "fmt"

// BusinessError 业务错误类型
type BusinessError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Error 实现error接口
func (e *BusinessError) Error() string {
	return e.Message
}

// GetCode 获取错误码
func (e *BusinessError) GetCode() int {
	return e.Code
}

// GetMessage 获取错误消息
func (e *BusinessError) GetMessage() string {
	return e.Message
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int, message string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
	}
}

// 预定义的业务错误
var (
	// 用户相关错误
	ErrUserNotFound     = NewBusinessError(1001, "用户不存在")
	ErrUserAlreadyExist = NewBusinessError(1002, "用户已存在")
	ErrInvalidPassword  = NewBusinessError(1003, "密码错误")
	ErrTokenExpired     = NewBusinessError(1004, "令牌已过期")
	ErrTokenInvalid     = NewBusinessError(1005, "令牌无效")
	ErrUserDisabled     = NewBusinessError(1006, "用户已被禁用")
	ErrEmailAlreadyExist = NewBusinessError(1007, "邮箱已被注册")
	
	// 通用错误
	ErrInvalidParams    = NewBusinessError(400, "参数错误")
	ErrUnauthorized     = NewBusinessError(401, "未授权")
	ErrForbidden        = NewBusinessError(403, "禁止访问")
	ErrNotFound         = NewBusinessError(404, "资源不存在")
	ErrInternalError    = NewBusinessError(500, "系统内部错误")
)

// IsBusinessError 判断是否为业务错误
func IsBusinessError(err error) (*BusinessError, bool) {
	if businessErr, ok := err.(*BusinessError); ok {
		return businessErr, true
	}
	return nil, false
}

// WrapError 包装普通错误为业务错误
func WrapError(err error, code int, message string) *BusinessError {
	if message == "" {
		message = err.Error()
	}
	return &BusinessError{
		Code:    code,
		Message: fmt.Sprintf("%s: %v", message, err),
	}
}
