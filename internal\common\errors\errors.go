package errors

import (
	"fmt"
	"net/http"
)

// BusinessError 业务错误结构体 - 匹配Java版本BusinessException
type BusinessError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Error 实现error接口
func (e *BusinessError) Error() string {
	return fmt.Sprintf("业务错误[%d]: %s", e.Code, e.Message)
}

// GetCode 获取错误码
func (e *BusinessError) GetCode() int {
	return e.Code
}

// GetMessage 获取错误消息
func (e *BusinessError) GetMessage() string {
	return e.Message
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int, message string) *BusinessError {
	// 如果没有提供消息，使用默认消息
	if message == "" {
		message = getDefaultMessage(code)
	}
	return &BusinessError{
		Code:    code,
		Message: message,
	}
}

// 错误消息映射 - 匹配Java版本
var errorMessages = map[int]string{
	200:  "操作成功",
	400:  "参数错误",
	401:  "未授权",
	403:  "禁止访问",
	404:  "资源不存在",
	405:  "方法不允许",
	500:  "系统内部错误",

	// 用户相关错误
	1001: "用户不存在",
	1002: "用户已存在",
	1003: "密码错误",
	1004: "令牌已过期",
	1005: "令牌无效",

	// 食物相关错误
	2001: "食物不存在",
	2002: "分类不存在",

	// 饮食记录相关错误
	3001: "饮食记录不存在",
	3002: "无效的用餐类型",

	// 文件相关错误
	4001: "文件不存在",
	4002: "不支持的文件类型",
	4003: "文件过大",
}

// GetMessage 获取错误消息 - 公开函数供其他包使用
func GetMessage(code int) string {
	if msg, ok := errorMessages[code]; ok {
		return msg
	}
	return "未知错误"
}

// getDefaultMessage 获取默认错误消息 - 内部使用
func getDefaultMessage(code int) string {
	return GetMessage(code)
}

// 预定义的业务错误 - 使用默认消息
var (
	// 通用错误
	ErrInvalidParams     = NewBusinessError(400, "")
	ErrUnauthorized      = NewBusinessError(401, "")
	ErrForbidden         = NewBusinessError(403, "")
	ErrNotFound          = NewBusinessError(404, "")
	ErrMethodNotAllowed  = NewBusinessError(405, "")
	ErrInternalError     = NewBusinessError(500, "")

	// 用户相关错误 - 匹配Java版本错误码
	ErrUserNotFound      = NewBusinessError(1001, "")
	ErrUserAlreadyExist  = NewBusinessError(1002, "")
	ErrInvalidPassword   = NewBusinessError(1003, "")
	ErrTokenExpired      = NewBusinessError(1004, "")
	ErrTokenInvalid      = NewBusinessError(1005, "")

	// 食物相关错误
	ErrFoodNotFound      = NewBusinessError(2001, "")
	ErrCategoryNotFound  = NewBusinessError(2002, "")

	// 饮食记录相关错误
	ErrDietRecordNotFound = NewBusinessError(3001, "")
	ErrInvalidMealType    = NewBusinessError(3002, "")

	// 文件相关错误
	ErrFileNotFound       = NewBusinessError(4001, "")
	ErrInvalidFileType    = NewBusinessError(4002, "")
	ErrFileTooLarge       = NewBusinessError(4003, "")
)

// IsBusinessError 判断是否为业务错误
func IsBusinessError(err error) bool {
	_, ok := err.(*BusinessError)
	return ok
}

// GetHTTPStatus 根据业务错误码获取HTTP状态码
func GetHTTPStatus(code int) int {
	switch {
	case code >= 400 && code < 500:
		return code
	case code >= 1000 && code < 2000: // 用户相关错误
		switch code {
		case 1001: // 用户不存在
			return http.StatusNotFound
		case 1002: // 用户已存在
			return http.StatusConflict
		case 1003: // 密码错误
			return http.StatusUnauthorized
		case 1004, 1005: // 令牌相关错误
			return http.StatusUnauthorized
		default:
			return http.StatusBadRequest
		}
	case code >= 2000 && code < 3000: // 食物相关错误
		switch code {
		case 2001, 2002: // 不存在类错误
			return http.StatusNotFound
		default:
			return http.StatusBadRequest
		}
	case code >= 3000 && code < 4000: // 饮食记录相关错误
		switch code {
		case 3001: // 记录不存在
			return http.StatusNotFound
		default:
			return http.StatusBadRequest
		}
	case code >= 4000 && code < 5000: // 文件相关错误
		switch code {
		case 4001: // 文件不存在
			return http.StatusNotFound
		case 4002, 4003: // 文件类型或大小错误
			return http.StatusBadRequest
		default:
			return http.StatusBadRequest
		}
	default:
		return http.StatusInternalServerError
	}
}
