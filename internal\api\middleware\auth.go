package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/infrastructure/auth"
)

// AuthMiddleware JWT认证中间件 - 匹配Java版本认证逻辑
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			response.ErrorWithMessage(c, 401, "缺少认证令牌")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			response.ErrorWithMessage(c, 401, "认证令牌格式错误")
			c.Abort()
			return
		}

		// 提取token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			response.ErrorWithMessage(c, 401, "认证令牌不能为空")
			c.Abort()
			return
		}

		// 验证token
		if !auth.ValidateToken(tokenString) {
			response.ErrorWithMessage(c, 401, "认证令牌无效或已过期")
			c.Abort()
			return
		}

		// 解析token获取用户信息
		claims, err := auth.ParseToken(tokenString)
		if err != nil {
			response.ErrorWithMessage(c, 401, "认证令牌解析失败")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中 - 匹配Java版本SecurityContextUtil
		c.Set("userId", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("token", tokenString)

		c.Next()
	}
}

// AdminOnlyMiddleware 管理员权限中间件 - 匹配Java版本@PreAuthorize("hasRole('ADMIN')")
func AdminOnlyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			response.ErrorWithMessage(c, 403, "权限验证失败")
			c.Abort()
			return
		}

		if role != "ADMIN" {
			response.ErrorWithMessage(c, 403, "需要管理员权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetCurrentUserID 从上下文获取当前用户ID - 匹配Java版本SecurityContextUtil.getCurrentUserId()
func GetCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := c.Get("userId")
	if !exists {
		return 0, false
	}
	
	if id, ok := userID.(int64); ok {
		return id, true
	}
	
	return 0, false
}

// GetCurrentUsername 从上下文获取当前用户名
func GetCurrentUsername(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}
	
	if name, ok := username.(string); ok {
		return name, true
	}
	
	return "", false
}

// GetCurrentUserRole 从上下文获取当前用户角色
func GetCurrentUserRole(c *gin.Context) (string, bool) {
	role, exists := c.Get("role")
	if !exists {
		return "", false
	}
	
	if r, ok := role.(string); ok {
		return r, true
	}
	
	return "", false
}

// RequireAuth 检查是否已认证的辅助函数
func RequireAuth(c *gin.Context) bool {
	_, exists := c.Get("userId")
	return exists
}
