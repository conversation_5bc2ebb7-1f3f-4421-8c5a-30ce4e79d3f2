package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ApiResponse 统一API响应格式 - 完全匹配Java版本
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// PageResult 分页结果 - 完全匹配Java版本
type PageResult struct {
	Total   int64       `json:"total"`   // 匹配Java的long类型
	Records interface{} `json:"records"` // 匹配Java的records字段名
	Current int         `json:"current"` // 匹配Java的current字段名
	Size    int         `json:"size"`    // 匹配Java的size字段名
}

// 错误消息映射
var messages = map[int]string{
	200:  "success",
	400:  "参数错误",
	401:  "未授权",
	403:  "禁止访问",
	404:  "资源不存在",
	500:  "系统内部错误",
	1001: "用户不存在",
	1002: "用户已存在",
	1003: "密码错误",
	1004: "令牌已过期",
	1005: "令牌无效",
}

// getMessage 获取错误消息
func getMessage(code int) string {
	if msg, ok := messages[code]; ok {
		return msg
	}
	return "未知错误"
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    code,
		Message: getMessage(code),
		Data:    nil,
	})
}

// ErrorWithMessage 错误响应（自定义消息）
func ErrorWithMessage(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, data interface{}, total int64, current, size int) {
	pageResult := PageResult{
		Total:   total,
		Records: data,
		Current: current,
		Size:    size,
	}
	Success(c, pageResult)
}

// BadRequest 400错误
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, ApiResponse{400, message, nil})
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, ApiResponse{401, message, nil})
}

// Forbidden 403错误
func Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, ApiResponse{403, message, nil})
}

// NotFound 404错误
func NotFound(c *gin.Context, message string) {
	c.JSON(http.StatusNotFound, ApiResponse{404, message, nil})
}

// InternalServerError 500错误
func InternalServerError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, ApiResponse{500, message, nil})
}
