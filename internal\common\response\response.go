package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ApiResponse 统一API响应格式 - 完全匹配Java版本
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// PageResult 分页结果 - 完全匹配Java版本
type PageResult struct {
	Total   int64       `json:"total"`   // 匹配Java的long类型
	Records interface{} `json:"records"` // 匹配Java的records字段名
	Current int         `json:"current"` // 匹配Java的current字段名
	Size    int         `json:"size"`    // 匹配Java的size字段名
}

// 响应状态码常量
const (
	SUCCESS = 200
	ERROR   = 500
	
	// 业务错误码
	INVALID_PARAMS     = 400
	UNAUTHORIZED       = 401
	FORBIDDEN          = 403
	NOT_FOUND          = 404
	METHOD_NOT_ALLOWED = 405
	
	// 自定义业务错误码
	USER_NOT_FOUND     = 1001
	USER_ALREADY_EXIST = 1002
	INVALID_PASSWORD   = 1003
	TOKEN_EXPIRED      = 1004
	TOKEN_INVALID      = 1005
)

// 错误消息映射
var codeMsg = map[int]string{
	SUCCESS:            "操作成功",
	ERROR:              "操作失败",
	INVALID_PARAMS:     "参数错误",
	UNAUTHORIZED:       "未授权",
	FORBIDDEN:          "禁止访问",
	NOT_FOUND:          "资源不存在",
	METHOD_NOT_ALLOWED: "方法不允许",
	USER_NOT_FOUND:     "用户不存在",
	USER_ALREADY_EXIST: "用户已存在",
	INVALID_PASSWORD:   "密码错误",
	TOKEN_EXPIRED:      "令牌已过期",
	TOKEN_INVALID:      "令牌无效",
}

// GetMsg 获取错误消息
func GetMsg(code int) string {
	msg, ok := codeMsg[code]
	if ok {
		return msg
	}
	return codeMsg[ERROR]
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    SUCCESS,
		Message: GetMsg(SUCCESS),
		Data:    data,
	})
}

// SuccessWithMessage 成功响应（自定义消息）
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    SUCCESS,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    code,
		Message: GetMsg(code),
		Data:    nil,
	})
}

// ErrorWithMessage 错误响应（自定义消息）
func ErrorWithMessage(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// ErrorWithData 错误响应（带数据）
func ErrorWithData(c *gin.Context, code int, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    code,
		Message: GetMsg(code),
		Data:    data,
	})
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, data interface{}, total int64, current, size int) {
	pageResult := PageResult{
		Total:   total,
		Records: data,
		Current: current,
		Size:    size,
	}
	
	Success(c, pageResult)
}

// BadRequest 400错误
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, ApiResponse{
		Code:    INVALID_PARAMS,
		Message: message,
		Data:    nil,
	})
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, ApiResponse{
		Code:    UNAUTHORIZED,
		Message: message,
		Data:    nil,
	})
}

// Forbidden 403错误
func Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, ApiResponse{
		Code:    FORBIDDEN,
		Message: message,
		Data:    nil,
	})
}

// NotFound 404错误
func NotFound(c *gin.Context, message string) {
	c.JSON(http.StatusNotFound, ApiResponse{
		Code:    NOT_FOUND,
		Message: message,
		Data:    nil,
	})
}

// InternalServerError 500错误
func InternalServerError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, ApiResponse{
		Code:    ERROR,
		Message: message,
		Data:    nil,
	})
}
